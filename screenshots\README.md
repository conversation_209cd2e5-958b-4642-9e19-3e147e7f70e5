# TalentSol Screenshots

This folder contains screenshots of the TalentSol Applicant Tracking System (ATS) application.

## Screenshots List

1. `landing-page.png` - The main landing page with navigation and hero section
2. `login-page.png` - The login page with sign-in form
3. `dashboard.png` - The main dashboard with recruitment metrics and pipeline
4. `candidate-pipeline.png` - The candidate pipeline board view with drag-and-drop functionality
5. `job-postings.png` - The job postings page with active listings
6. `interviews.png` - The interview scheduling calendar view
7. `analytics-overview.png` - The analytics overview page with core reports
8. `pipeline-metrics.png` - Detailed pipeline metrics report
9. `time-to-hire.png` - Time to hire analytics by department (chart view)
10. `time-to-hire-table.png` - Time to hire analytics by department (table view)
11. `custom-reports.png` - Custom reports section
12. `ml-reports.png` - Machine learning reports section
13. `messages.png` - Messages inbox
14. `documents.png` - Document management with <PERSON> assistant

These screenshots showcase the complete TalentSol ATS application built with React, TypeScript, Tailwind CSS, and Vite.
